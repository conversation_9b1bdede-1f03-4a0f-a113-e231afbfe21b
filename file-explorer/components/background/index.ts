// components/background/index.ts

// Database Manager (Main process only) - Conditionally exported
export type { DatabaseConfig, Migration } from './database-manager';

// Configuration Store (Main process only) - Conditionally exported
export type { ProjectConfig, NamingConventions, CodeArchitecture, StyleGuide, GlobalSettings } from './config-store';

// Dynamic exports for main process only
if (typeof window === 'undefined') {
  // Only export these in Node.js/main process environment
  try {
    const dbModule = require('./database-manager');
    const configModule = require('./config-store');

    module.exports.DatabaseManager = dbModule.DatabaseManager;
    module.exports.getDatabaseManager = dbModule.getDatabaseManager;
    module.exports.closeDatabaseManager = dbModule.closeDatabaseManager;
    module.exports.ConfigStore = configModule.ConfigStore;
    module.exports.getConfigStore = configModule.getConfigStore;
  } catch (error) {
    console.warn('Main process modules not available in browser environment');
  }
}

// Monaco Integration Systems (Browser-safe exports)
export {
  MonacoIntegration,
  getMonacoIntegration,
  shutdownMonacoIntegration,
  type MonacoEditorInstance,
  type EditorOperation,
  type EditorEvent,
  type MonacoIntegrationStats
} from './monaco-integration';

export {
  SyntaxAnalyzer,
  getSyntaxAnalyzer,
  shutdownSyntaxAnalyzer,
  type SyntaxAnalysisResult,
  type SyntaxToken,
  type SymbolInfo,
  type DependencyInfo,
  type SyntaxIssue,
  type CodeSuggestion,
  type SyntaxAnalyzerConfig
} from './syntax-analyzer';

export {
  ErrorDetector,
  getErrorDetector,
  shutdownErrorDetector,
  type ErrorDetectionResult,
  type DetectedError,
  type DetectedWarning,
  type ErrorSuggestion,
  type ErrorDetectorConfig
} from './error-detector';

export {
  CodeCompletionEnhancer,
  getCodeCompletionEnhancer,
  shutdownCodeCompletionEnhancer,
  type CompletionItem,
  type CompletionContext,
  type CompletionResult,
  type CodeCompletionConfig,
  CompletionItemKind
} from './code-completion-enhancer';

// Browser-safe Configuration Store (Renderer process)
export {
  ConfigStoreBrowser,
  getConfigStoreBrowser,
  type ProjectConfig,
  type NamingConventions,
  type CodeArchitecture,
  type StyleGuide,
  type GlobalSettings
} from './config-store-browser';

// Default Configurations
export {
  DEFAULT_NAMING_CONVENTIONS,
  DEFAULT_CODE_ARCHITECTURE,
  DEFAULT_STYLE_GUIDE,
  TYPESCRIPT_NAMING_CONVENTIONS,
  REACT_CODE_ARCHITECTURE,
  NEXTJS_STYLE_GUIDE,
  PROJECT_TEMPLATES,
  getProjectTemplate,
  createDefaultProjectConfig
} from './default-configs';

// Vector Database (Browser-safe)
export {
  BasicVectorDatabase,
  type VectorDocument,
  type SearchResult,
  type VectorSearchOptions
} from './vector-database';

// Code Indexer (Browser-safe)
export {
  CodeIndexer,
  type IndexingOptions,
  type IndexingProgress,
  type FileChunk
} from './code-indexer';

// Semantic Search (Browser-safe)
export {
  SemanticSearchService,
  type SemanticSearchQuery,
  type CodeContext,
  type SearchSuggestion
} from './semantic-search';

// Context Prefetcher (Browser-safe)
export {
  ContextPrefetcher,
  getContextPrefetcher,
  type PrefetchRequest,
  type PrefetchedContext,
  type ContextItem,
  type PrefetchConfig,
  type PrefetchStats
} from './context-prefetcher';

// Context Relevance Scorer (Browser-safe)
export {
  ContextRelevanceScorer,
  getContextRelevanceScorer,
  type RelevanceFactors,
  type ScoringWeights,
  type ScoringConfig,
  type ScoringResult
} from './context-relevance-scorer';

// Context Cache Manager (Browser-safe)
export {
  ContextCacheManager,
  getContextCacheManager,
  type CacheEntry,
  type CacheStats,
  type CacheConfig
} from './context-cache-manager';

// Communication System (Browser-safe)
export {
  MessageBus,
  globalMessageBus,
  type AgentMessage,
  type MessageHandler,
  type MessageSubscription,
  type MessageBusStats
} from './message-bus';

// Task Queue System (Browser-safe)
export {
  TaskQueue,
  globalTaskQueue,
  type AgentTask,
  type AgentCapability,
  type TaskQueueStats,
  type TaskFilter,
  type TaskAssignmentStrategy
} from './task-queue';

// Agent Registry System (Browser-safe)
export {
  AgentRegistry,
  globalAgentRegistry,
  type AgentRegistration,
  type AgentHealthMetrics,
  type ServiceDiscoveryQuery,
  type AgentRegistryStats,
  type AgentEvent,
  type AgentEventHandler
} from './agent-registry';

// Coordination Protocols System (Browser-safe)
export {
  CoordinationProtocols,
  globalCoordinationProtocols,
  type WorkflowStep,
  type Workflow,
  type RetryPolicy,
  type RollbackAction,
  type ResourceLock,
  type ConflictResolution,
  type CoordinationEvent,
  type CoordinationEventHandler,
  type CoordinationStats
} from './coordination-protocols';

// Project Dictionary System (Browser-safe)
export {
  ProjectDictionary,
  getProjectDictionary,
  clearProjectDictionary,
  getAllProjectDictionaries,
  type DictionaryTerm,
  type NamingRule,
  type ConsistencyViolation,
  type DictionaryStats,
  type TermRelationship,
  type TermSearchQuery,
  type TermSearchResult
} from './project-dictionary';

// File Operations System (Browser-safe)
export {
  FileOperationsManager,
  globalFileOperations,
  type FileOperation,
  type FileOperationOptions,
  type FileOperationResult,
  type FilePermissions,
  type SecurityPolicy,
  type FileOperationStats
} from './file-operations';

// File Transaction Manager (Browser-safe)
export {
  FileTransactionManager,
  globalFileTransactionManager,
  type FileTransaction,
  type TransactionCheckpoint,
  type TransactionResult,
  type RollbackResult,
  type TransactionStats
} from './file-transaction-manager';

// File System Monitor (Browser-safe)
export {
  FileSystemMonitor,
  globalFileSystemMonitor,
  type FileSystemEvent,
  type WatchTarget,
  type FileSystemStats,
  type FileSystemEventHandler
} from './file-system-monitor';

// Background System Initialization (Browser-safe)
export async function initializeBackgroundSystems(): Promise<void> {
  try {
    console.log('Initializing background systems...');

    // Use browser-safe config store
    const configStore = getConfigStoreBrowser();
    await configStore.initialize();

    console.log('Background systems initialized successfully');
  } catch (error) {
    console.error('Failed to initialize background systems:', error);
    throw error;
  }
}

// Background System Cleanup (Browser-safe)
export function cleanupBackgroundSystems(): void {
  try {
    console.log('Cleaning up background systems...');

    // Shutdown communication systems
    globalMessageBus.shutdown();
    globalTaskQueue.shutdown();
    globalAgentRegistry.shutdown();
    globalCoordinationProtocols.shutdown();

    console.log('Background systems cleaned up successfully');
  } catch (error) {
    console.error('Failed to cleanup background systems:', error);
  }
}

// Health Check (Browser-safe)
export async function checkBackgroundSystemsHealth(): Promise<{
  database: boolean;
  configStore: boolean;
  messageBus: boolean;
  taskQueue: boolean;
  agentRegistry: boolean;
  coordinationProtocols: boolean;
  projectDictionary: boolean;
  fileOperations: boolean;
  fileTransactionManager: boolean;
  fileSystemMonitor: boolean;
  overall: boolean;
}> {
  const health = {
    database: false,
    configStore: false,
    messageBus: false,
    taskQueue: false,
    agentRegistry: false,
    coordinationProtocols: false,
    projectDictionary: false,
    fileOperations: false,
    fileTransactionManager: false,
    fileSystemMonitor: false,
    overall: false
  };

  try {
    // Check if we're in Electron environment
    if (typeof window !== 'undefined' && window.electronAPI) {
      health.database = true; // Assume database is working if IPC is available
    }

    // Check config store
    const configStore = getConfigStoreBrowser();
    health.configStore = configStore.isInitialized();

    // Check communication systems
    health.messageBus = globalMessageBus.getStats().totalMessages >= 0; // Basic check
    health.taskQueue = globalTaskQueue.getStats().totalTasks >= 0; // Basic check
    health.agentRegistry = globalAgentRegistry.getStats().totalAgents >= 0; // Basic check
    health.coordinationProtocols = globalCoordinationProtocols.getStats().activeWorkflows >= 0; // Basic check

    // Check project dictionary (basic check with default project)
    const defaultDictionary = getProjectDictionary('default');
    health.projectDictionary = defaultDictionary.getStats().totalTerms >= 0; // Basic check

    // Check file systems
    health.fileOperations = globalFileOperations.getStats().totalOperations >= 0; // Basic check
    health.fileTransactionManager = globalFileTransactionManager.getStats().totalTransactions >= 0; // Basic check
    health.fileSystemMonitor = globalFileSystemMonitor.getStats().totalEvents >= 0; // Basic check

    health.overall = health.configStore && health.messageBus && health.taskQueue &&
                    health.agentRegistry && health.coordinationProtocols && health.projectDictionary &&
                    health.fileOperations && health.fileTransactionManager && health.fileSystemMonitor;
  } catch (error) {
    console.error('Background systems health check failed:', error);
  }

  return health;
}
